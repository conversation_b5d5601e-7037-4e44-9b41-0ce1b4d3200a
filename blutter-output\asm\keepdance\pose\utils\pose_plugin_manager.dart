// lib: , url: package:keepdance/pose/utils/pose_plugin_manager.dart

// class id: 1050114, size: 0x8
class :: {
}

// class id: 1693, size: 0x38, field offset: 0x18
class PosePluginManager extends GetxService {

  _ initialize(/* No info */) async {
    // ** addr: 0x6c78d8, size: 0x53c
    // 0x6c78d8: EnterFrame
    //     0x6c78d8: stp             fp, lr, [SP, #-0x10]!
    //     0x6c78dc: mov             fp, SP
    // 0x6c78e0: AllocStack(0xb8)
    //     0x6c78e0: sub             SP, SP, #0xb8
    // 0x6c78e4: SetupParameters(PosePluginManager this /* r1 => r1, fp-0x90 */, {dynamic forceReinitialize = false /* r3, fp-0x88 */, int maxPoses = 1 /* r5, fp-0x78 */})
    //     0x6c78e4: stur            NULL, [fp, #-8]
    //     0x6c78e8: stur            x1, [fp, #-0x90]
    //     0x6c78ec: stur            x4, [fp, #-0x98]
    //     0x6c78f0: ldur            w0, [x4, #0x13]
    //     0x6c78f4: ldur            w2, [x4, #0x1f]
    //     0x6c78f8: add             x2, x2, HEAP, lsl #32
    //     0x6c78fc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa558] "forceReinitialize"
    //     0x6c7900: ldr             x16, [x16, #0x558]
    //     0x6c7904: cmp             w2, w16
    //     0x6c7908: b.ne            #0x6c792c
    //     0x6c790c: ldur            w2, [x4, #0x23]
    //     0x6c7910: add             x2, x2, HEAP, lsl #32
    //     0x6c7914: sub             w3, w0, w2
    //     0x6c7918: add             x2, fp, w3, sxtw #2
    //     0x6c791c: ldr             x2, [x2, #8]
    //     0x6c7920: mov             x3, x2
    //     0x6c7924: movz            x2, #0x1
    //     0x6c7928: b               #0x6c7934
    //     0x6c792c: add             x3, NULL, #0x30  ; false
    //     0x6c7930: movz            x2, #0
    //     0x6c7934: stur            x3, [fp, #-0x88]
    //     0x6c7938: lsl             x5, x2, #1
    //     0x6c793c: lsl             w6, w5, #1
    //     0x6c7940: add             w7, w6, #8
    //     0x6c7944: add             x16, x4, w7, sxtw #1
    //     0x6c7948: ldur            w8, [x16, #0xf]
    //     0x6c794c: add             x8, x8, HEAP, lsl #32
    //     0x6c7950: add             x16, PP, #0xa, lsl #12  ; [pp+0xa560] "maxPoses"
    //     0x6c7954: ldr             x16, [x16, #0x560]
    //     0x6c7958: cmp             w8, w16
    //     0x6c795c: b.ne            #0x6c7998
    //     0x6c7960: add             w2, w6, #0xa
    //     0x6c7964: add             x16, x4, w2, sxtw #1
    //     0x6c7968: ldur            w6, [x16, #0xf]
    //     0x6c796c: add             x6, x6, HEAP, lsl #32
    //     0x6c7970: sub             w2, w0, w6
    //     0x6c7974: add             x0, fp, w2, sxtw #2
    //     0x6c7978: ldr             x0, [x0, #8]
    //     0x6c797c: add             w2, w5, #2
    //     0x6c7980: sbfx            x5, x0, #1, #0x1f
    //     0x6c7984: tbz             w0, #0, #0x6c798c
    //     0x6c7988: ldur            x5, [x0, #7]
    //     0x6c798c: sbfx            x0, x2, #1, #0x1f
    //     0x6c7990: mov             x2, x0
    //     0x6c7994: b               #0x6c799c
    //     0x6c7998: movz            x5, #0x1
    //     0x6c799c: stur            x5, [fp, #-0x78]
    //     0x6c79a0: stur            x2, [fp, #-0x80]
    // 0x6c79a4: CheckStackOverflow
    //     0x6c79a4: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c79a8: cmp             SP, x16
    //     0x6c79ac: b.ls            #0x6c7e0c
    // 0x6c79b0: InitAsync() -> Future<void?>
    //     0x6c79b0: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6c79b4: bl              #0x61100c  ; InitAsyncStub
    // 0x6c79b8: ldur            x0, [fp, #-0x90]
    // 0x6c79bc: LoadField: r1 = r0->field_23
    //     0x6c79bc: ldur            w1, [x0, #0x23]
    // 0x6c79c0: DecompressPointer r1
    //     0x6c79c0: add             x1, x1, HEAP, lsl #32
    // 0x6c79c4: tbnz            w1, #4, #0x6c7c94
    // 0x6c79c8: ldur            x2, [fp, #-0x88]
    // 0x6c79cc: tbz             w2, #4, #0x6c7c8c
    // 0x6c79d0: ldur            x3, [fp, #-0x78]
    // 0x6c79d4: LoadField: r1 = r0->field_27
    //     0x6c79d4: ldur            x1, [x0, #0x27]
    // 0x6c79d8: cmp             x1, x3
    // 0x6c79dc: b.ne            #0x6c7a4c
    // 0x6c79e0: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6c79e0: ldur            w4, [x0, #0x17]
    // 0x6c79e4: DecompressPointer r4
    //     0x6c79e4: add             x4, x4, HEAP, lsl #32
    // 0x6c79e8: stur            x4, [fp, #-0x98]
    // 0x6c79ec: r1 = Null
    //     0x6c79ec: mov             x1, NULL
    // 0x6c79f0: r2 = 6
    //     0x6c79f0: movz            x2, #0x6
    // 0x6c79f4: r0 = AllocateArray()
    //     0x6c79f4: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c79f8: mov             x2, x0
    // 0x6c79fc: r16 = "PosePluginManager: 参数无变化，跳过初始化 (maxPoses: "
    //     0x6c79fc: add             x16, PP, #0xa, lsl #12  ; [pp+0xa568] "PosePluginManager: 参数无变化，跳过初始化 (maxPoses: "
    //     0x6c7a00: ldr             x16, [x16, #0x568]
    // 0x6c7a04: StoreField: r2->field_f = r16
    //     0x6c7a04: stur            w16, [x2, #0xf]
    // 0x6c7a08: ldur            x3, [fp, #-0x78]
    // 0x6c7a0c: r0 = BoxInt64Instr(r3)
    //     0x6c7a0c: sbfiz           x0, x3, #1, #0x1f
    //     0x6c7a10: cmp             x3, x0, asr #1
    //     0x6c7a14: b.eq            #0x6c7a20
    //     0x6c7a18: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c7a1c: stur            x3, [x0, #7]
    // 0x6c7a20: StoreField: r2->field_13 = r0
    //     0x6c7a20: stur            w0, [x2, #0x13]
    // 0x6c7a24: r16 = ")"
    //     0x6c7a24: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0x6c7a28: ArrayStore: r2[0] = r16  ; List_4
    //     0x6c7a28: stur            w16, [x2, #0x17]
    // 0x6c7a2c: str             x2, [SP]
    // 0x6c7a30: r0 = _interpolate()
    //     0x6c7a30: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c7a34: ldur            x1, [fp, #-0x98]
    // 0x6c7a38: mov             x2, x0
    // 0x6c7a3c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7a3c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7a40: r0 = d()
    //     0x6c7a40: bl              #0x66a1dc  ; [package:logger/src/logger.dart] Logger::d
    // 0x6c7a44: r0 = Null
    //     0x6c7a44: mov             x0, NULL
    // 0x6c7a48: r0 = ReturnAsyncNotFuture()
    //     0x6c7a48: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6c7a4c: ldur            x1, [fp, #-0x90]
    // 0x6c7a50: r0 = _isDetectionQualityGood()
    //     0x6c7a50: bl              #0x6c9868  ; [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_isDetectionQualityGood
    // 0x6c7a54: tbnz            w0, #4, #0x6c7c5c
    // 0x6c7a58: ldur            x3, [fp, #-0x90]
    // 0x6c7a5c: ldur            x0, [fp, #-0x78]
    // 0x6c7a60: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x6c7a60: ldur            w4, [x3, #0x17]
    // 0x6c7a64: DecompressPointer r4
    //     0x6c7a64: add             x4, x4, HEAP, lsl #32
    // 0x6c7a68: stur            x4, [fp, #-0x98]
    // 0x6c7a6c: r1 = Null
    //     0x6c7a6c: mov             x1, NULL
    // 0x6c7a70: r2 = 10
    //     0x6c7a70: movz            x2, #0xa
    // 0x6c7a74: r0 = AllocateArray()
    //     0x6c7a74: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c7a78: mov             x2, x0
    // 0x6c7a7c: r16 = "PosePluginManager: 检测质量良好，尝试参数热更新 ("
    //     0x6c7a7c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa570] "PosePluginManager: 检测质量良好，尝试参数热更新 ("
    //     0x6c7a80: ldr             x16, [x16, #0x570]
    // 0x6c7a84: StoreField: r2->field_f = r16
    //     0x6c7a84: stur            w16, [x2, #0xf]
    // 0x6c7a88: ldur            x3, [fp, #-0x90]
    // 0x6c7a8c: LoadField: r4 = r3->field_27
    //     0x6c7a8c: ldur            x4, [x3, #0x27]
    // 0x6c7a90: r0 = BoxInt64Instr(r4)
    //     0x6c7a90: sbfiz           x0, x4, #1, #0x1f
    //     0x6c7a94: cmp             x4, x0, asr #1
    //     0x6c7a98: b.eq            #0x6c7aa4
    //     0x6c7a9c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c7aa0: stur            x4, [x0, #7]
    // 0x6c7aa4: StoreField: r2->field_13 = r0
    //     0x6c7aa4: stur            w0, [x2, #0x13]
    // 0x6c7aa8: r16 = " -> "
    //     0x6c7aa8: ldr             x16, [PP, #0x6aa0]  ; [pp+0x6aa0] " -> "
    // 0x6c7aac: ArrayStore: r2[0] = r16  ; List_4
    //     0x6c7aac: stur            w16, [x2, #0x17]
    // 0x6c7ab0: ldur            x4, [fp, #-0x78]
    // 0x6c7ab4: r0 = BoxInt64Instr(r4)
    //     0x6c7ab4: sbfiz           x0, x4, #1, #0x1f
    //     0x6c7ab8: cmp             x4, x0, asr #1
    //     0x6c7abc: b.eq            #0x6c7ac8
    //     0x6c7ac0: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c7ac4: stur            x4, [x0, #7]
    // 0x6c7ac8: stur            x0, [fp, #-0xa0]
    // 0x6c7acc: StoreField: r2->field_1b = r0
    //     0x6c7acc: stur            w0, [x2, #0x1b]
    // 0x6c7ad0: r16 = ")"
    //     0x6c7ad0: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0x6c7ad4: StoreField: r2->field_1f = r16
    //     0x6c7ad4: stur            w16, [x2, #0x1f]
    // 0x6c7ad8: str             x2, [SP]
    // 0x6c7adc: r0 = _interpolate()
    //     0x6c7adc: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c7ae0: ldur            x1, [fp, #-0x98]
    // 0x6c7ae4: mov             x2, x0
    // 0x6c7ae8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7ae8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7aec: r0 = i()
    //     0x6c7aec: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c7af0: ldur            x0, [fp, #-0x90]
    // 0x6c7af4: LoadField: r1 = r0->field_27
    //     0x6c7af4: ldur            x1, [x0, #0x27]
    // 0x6c7af8: ldur            x2, [fp, #-0x78]
    // 0x6c7afc: cmp             x2, x1
    // 0x6c7b00: b.ge            #0x6c7b3c
    // 0x6c7b04: mov             x1, x0
    // 0x6c7b08: r0 = _shouldAllowDowngrade()
    //     0x6c7b08: bl              #0x6c9770  ; [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_shouldAllowDowngrade
    // 0x6c7b0c: tbnz            w0, #4, #0x6c7b3c
    // 0x6c7b10: ldur            x1, [fp, #-0x98]
    // 0x6c7b14: r2 = "PosePluginManager: 检测效果不佳，允许降级重新初始化"
    //     0x6c7b14: add             x2, PP, #0xa, lsl #12  ; [pp+0xa578] "PosePluginManager: 检测效果不佳，允许降级重新初始化"
    //     0x6c7b18: ldr             x2, [x2, #0x578]
    // 0x6c7b1c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7b1c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7b20: r0 = i()
    //     0x6c7b20: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c7b24: ldur            x4, [fp, #-0x90]
    // 0x6c7b28: ldur            x3, [fp, #-0xa0]
    // 0x6c7b2c: ldur            x2, [fp, #-0x88]
    // 0x6c7b30: r1 = Null
    //     0x6c7b30: mov             x1, NULL
    // 0x6c7b34: r0 = Null
    //     0x6c7b34: mov             x0, NULL
    // 0x6c7b38: b               #0x6c7d3c
    // 0x6c7b3c: ldur            x0, [fp, #-0x90]
    // 0x6c7b40: ldur            x3, [fp, #-0x78]
    // 0x6c7b44: LoadField: r4 = r0->field_27
    //     0x6c7b44: ldur            x4, [x0, #0x27]
    // 0x6c7b48: stur            x4, [fp, #-0x80]
    // 0x6c7b4c: cmp             x3, x4
    // 0x6c7b50: b.le            #0x6c7c40
    // 0x6c7b54: ldur            x5, [fp, #-0xa0]
    // 0x6c7b58: r1 = Null
    //     0x6c7b58: mov             x1, NULL
    // 0x6c7b5c: r2 = 10
    //     0x6c7b5c: movz            x2, #0xa
    // 0x6c7b60: r0 = AllocateArray()
    //     0x6c7b60: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c7b64: mov             x2, x0
    // 0x6c7b68: r16 = "PosePluginManager: 单人→双人，需要更新原生层maxPoses参数 ("
    //     0x6c7b68: add             x16, PP, #0xa, lsl #12  ; [pp+0xa580] "PosePluginManager: 单人→双人，需要更新原生层maxPoses参数 ("
    //     0x6c7b6c: ldr             x16, [x16, #0x580]
    // 0x6c7b70: StoreField: r2->field_f = r16
    //     0x6c7b70: stur            w16, [x2, #0xf]
    // 0x6c7b74: ldur            x3, [fp, #-0x80]
    // 0x6c7b78: r0 = BoxInt64Instr(r3)
    //     0x6c7b78: sbfiz           x0, x3, #1, #0x1f
    //     0x6c7b7c: cmp             x3, x0, asr #1
    //     0x6c7b80: b.eq            #0x6c7b8c
    //     0x6c7b84: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c7b88: stur            x3, [x0, #7]
    // 0x6c7b8c: StoreField: r2->field_13 = r0
    //     0x6c7b8c: stur            w0, [x2, #0x13]
    // 0x6c7b90: r16 = " -> "
    //     0x6c7b90: ldr             x16, [PP, #0x6aa0]  ; [pp+0x6aa0] " -> "
    // 0x6c7b94: ArrayStore: r2[0] = r16  ; List_4
    //     0x6c7b94: stur            w16, [x2, #0x17]
    // 0x6c7b98: ldur            x0, [fp, #-0xa0]
    // 0x6c7b9c: StoreField: r2->field_1b = r0
    //     0x6c7b9c: stur            w0, [x2, #0x1b]
    // 0x6c7ba0: r16 = ")"
    //     0x6c7ba0: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0x6c7ba4: StoreField: r2->field_1f = r16
    //     0x6c7ba4: stur            w16, [x2, #0x1f]
    // 0x6c7ba8: str             x2, [SP]
    // 0x6c7bac: r0 = _interpolate()
    //     0x6c7bac: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c7bb0: ldur            x1, [fp, #-0x98]
    // 0x6c7bb4: mov             x2, x0
    // 0x6c7bb8: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7bb8: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7bbc: r0 = i()
    //     0x6c7bbc: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c7bc0: ldur            x2, [fp, #-0x90]
    // 0x6c7bc4: ldur            x3, [fp, #-0x78]
    // 0x6c7bc8: ldur            x0, [fp, #-0xa0]
    // 0x6c7bcc: LoadField: r1 = r2->field_1b
    //     0x6c7bcc: ldur            w1, [x2, #0x1b]
    // 0x6c7bd0: DecompressPointer r1
    //     0x6c7bd0: add             x1, x1, HEAP, lsl #32
    // 0x6c7bd4: r16 = false
    //     0x6c7bd4: add             x16, NULL, #0x30  ; false
    // 0x6c7bd8: stp             x16, x0, [SP]
    // 0x6c7bdc: r4 = const [0, 0x3, 0x2, 0x1, forceReinitialize, 0x2, maxPoses, 0x1, null]
    //     0x6c7bdc: add             x4, PP, #0xa, lsl #12  ; [pp+0xa588] List(9) [0, 0x3, 0x2, 0x1, "forceReinitialize", 0x2, "maxPoses", 0x1, Null]
    //     0x6c7be0: ldr             x4, [x4, #0x588]
    // 0x6c7be4: r0 = initialize()
    //     0x6c7be4: bl              #0x6c84f8  ; [package:keepdance/pose/utils/pose_plugin.dart] PosePlugin::initialize
    // 0x6c7be8: mov             x1, x0
    // 0x6c7bec: stur            x1, [fp, #-0xa8]
    // 0x6c7bf0: r0 = Await()
    //     0x6c7bf0: bl              #0x610dcc  ; AwaitStub
    // 0x6c7bf4: ldur            x0, [fp, #-0x90]
    // 0x6c7bf8: ldur            x3, [fp, #-0x78]
    // 0x6c7bfc: StoreField: r0->field_27 = r3
    //     0x6c7bfc: stur            x3, [x0, #0x27]
    // 0x6c7c00: r1 = Null
    //     0x6c7c00: mov             x1, NULL
    // 0x6c7c04: r2 = 4
    //     0x6c7c04: movz            x2, #0x4
    // 0x6c7c08: r0 = AllocateArray()
    //     0x6c7c08: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c7c0c: r16 = "PosePluginManager: 成功更新原生层maxPoses参数到"
    //     0x6c7c0c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa590] "PosePluginManager: 成功更新原生层maxPoses参数到"
    //     0x6c7c10: ldr             x16, [x16, #0x590]
    // 0x6c7c14: StoreField: r0->field_f = r16
    //     0x6c7c14: stur            w16, [x0, #0xf]
    // 0x6c7c18: ldur            x1, [fp, #-0xa0]
    // 0x6c7c1c: StoreField: r0->field_13 = r1
    //     0x6c7c1c: stur            w1, [x0, #0x13]
    // 0x6c7c20: str             x0, [SP]
    // 0x6c7c24: r0 = _interpolate()
    //     0x6c7c24: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c7c28: ldur            x1, [fp, #-0x98]
    // 0x6c7c2c: mov             x2, x0
    // 0x6c7c30: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7c30: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7c34: r0 = i()
    //     0x6c7c34: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c7c38: r0 = Null
    //     0x6c7c38: mov             x0, NULL
    // 0x6c7c3c: r0 = ReturnAsyncNotFuture()
    //     0x6c7c3c: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6c7c40: ldur            x1, [fp, #-0x98]
    // 0x6c7c44: r2 = "PosePluginManager: 保持当前检测器状态"
    //     0x6c7c44: add             x2, PP, #0xa, lsl #12  ; [pp+0xa598] "PosePluginManager: 保持当前检测器状态"
    //     0x6c7c48: ldr             x2, [x2, #0x598]
    // 0x6c7c4c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7c4c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7c50: r0 = d()
    //     0x6c7c50: bl              #0x66a1dc  ; [package:logger/src/logger.dart] Logger::d
    // 0x6c7c54: r0 = Null
    //     0x6c7c54: mov             x0, NULL
    // 0x6c7c58: r0 = ReturnAsyncNotFuture()
    //     0x6c7c58: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6c7c5c: ldur            x2, [fp, #-0x78]
    // 0x6c7c60: r0 = BoxInt64Instr(r2)
    //     0x6c7c60: sbfiz           x0, x2, #1, #0x1f
    //     0x6c7c64: cmp             x2, x0, asr #1
    //     0x6c7c68: b.eq            #0x6c7c74
    //     0x6c7c6c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c7c70: stur            x2, [x0, #7]
    // 0x6c7c74: ldur            x4, [fp, #-0x90]
    // 0x6c7c78: mov             x3, x0
    // 0x6c7c7c: ldur            x2, [fp, #-0x88]
    // 0x6c7c80: r1 = Null
    //     0x6c7c80: mov             x1, NULL
    // 0x6c7c84: r0 = Null
    //     0x6c7c84: mov             x0, NULL
    // 0x6c7c88: b               #0x6c7d3c
    // 0x6c7c8c: ldur            x2, [fp, #-0x78]
    // 0x6c7c90: b               #0x6c7c98
    // 0x6c7c94: ldur            x2, [fp, #-0x78]
    // 0x6c7c98: r0 = BoxInt64Instr(r2)
    //     0x6c7c98: sbfiz           x0, x2, #1, #0x1f
    //     0x6c7c9c: cmp             x2, x0, asr #1
    //     0x6c7ca0: b.eq            #0x6c7cac
    //     0x6c7ca4: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c7ca8: stur            x2, [x0, #7]
    // 0x6c7cac: ldur            x6, [fp, #-0x90]
    // 0x6c7cb0: mov             x5, x0
    // 0x6c7cb4: ldur            x4, [fp, #-0x88]
    // 0x6c7cb8: r3 = Null
    //     0x6c7cb8: mov             x3, NULL
    // 0x6c7cbc: r0 = Null
    //     0x6c7cbc: mov             x0, NULL
    // 0x6c7cc0: b               #0x6c7d4c
    // 0x6c7cc4: sub             SP, fp, #0xb8
    // 0x6c7cc8: ldur            x4, [fp, #-0x10]
    // 0x6c7ccc: mov             x3, x0
    // 0x6c7cd0: stur            x0, [fp, #-0x90]
    // 0x6c7cd4: mov             x0, x1
    // 0x6c7cd8: stur            x1, [fp, #-0x98]
    // 0x6c7cdc: ArrayLoad: r5 = r4[0]  ; List_4
    //     0x6c7cdc: ldur            w5, [x4, #0x17]
    // 0x6c7ce0: DecompressPointer r5
    //     0x6c7ce0: add             x5, x5, HEAP, lsl #32
    // 0x6c7ce4: stur            x5, [fp, #-0x88]
    // 0x6c7ce8: r1 = Null
    //     0x6c7ce8: mov             x1, NULL
    // 0x6c7cec: r2 = 4
    //     0x6c7cec: movz            x2, #0x4
    // 0x6c7cf0: r0 = AllocateArray()
    //     0x6c7cf0: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c7cf4: r16 = "PosePluginManager: 更新原生层参数失败，将进行完整重新初始化: "
    //     0x6c7cf4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa5a0] "PosePluginManager: 更新原生层参数失败，将进行完整重新初始化: "
    //     0x6c7cf8: ldr             x16, [x16, #0x5a0]
    // 0x6c7cfc: StoreField: r0->field_f = r16
    //     0x6c7cfc: stur            w16, [x0, #0xf]
    // 0x6c7d00: ldur            x1, [fp, #-0x90]
    // 0x6c7d04: StoreField: r0->field_13 = r1
    //     0x6c7d04: stur            w1, [x0, #0x13]
    // 0x6c7d08: str             x0, [SP]
    // 0x6c7d0c: r0 = _interpolate()
    //     0x6c7d0c: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c7d10: ldur            x1, [fp, #-0x88]
    // 0x6c7d14: mov             x2, x0
    // 0x6c7d18: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7d18: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7d1c: r0 = w()
    //     0x6c7d1c: bl              #0x61c68c  ; [package:logger/src/logger.dart] Logger::w
    // 0x6c7d20: ldur            x1, [fp, #-0x18]
    // 0x6c7d24: ldur            x0, [fp, #-0x20]
    // 0x6c7d28: ldur            x4, [fp, #-0x10]
    // 0x6c7d2c: mov             x3, x1
    // 0x6c7d30: mov             x2, x0
    // 0x6c7d34: ldur            x1, [fp, #-0x90]
    // 0x6c7d38: ldur            x0, [fp, #-0x98]
    // 0x6c7d3c: mov             x6, x4
    // 0x6c7d40: mov             x5, x3
    // 0x6c7d44: mov             x4, x2
    // 0x6c7d48: mov             x3, x1
    // 0x6c7d4c: mov             x1, x6
    // 0x6c7d50: mov             x2, x4
    // 0x6c7d54: stur            x6, [fp, #-0x88]
    // 0x6c7d58: stur            x5, [fp, #-0x90]
    // 0x6c7d5c: stur            x4, [fp, #-0x98]
    // 0x6c7d60: stur            x3, [fp, #-0xa0]
    // 0x6c7d64: stur            x0, [fp, #-0xa8]
    // 0x6c7d68: r0 = _shouldReinitialize()
    //     0x6c7d68: bl              #0x6c8328  ; [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_shouldReinitialize
    // 0x6c7d6c: tbz             w0, #4, #0x6c7d94
    // 0x6c7d70: ldur            x0, [fp, #-0x88]
    // 0x6c7d74: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6c7d74: ldur            w1, [x0, #0x17]
    // 0x6c7d78: DecompressPointer r1
    //     0x6c7d78: add             x1, x1, HEAP, lsl #32
    // 0x6c7d7c: r2 = "PosePluginManager: 智能判断不需要重新初始化"
    //     0x6c7d7c: add             x2, PP, #0xa, lsl #12  ; [pp+0xa5a8] "PosePluginManager: 智能判断不需要重新初始化"
    //     0x6c7d80: ldr             x2, [x2, #0x5a8]
    // 0x6c7d84: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7d84: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7d88: r0 = d()
    //     0x6c7d88: bl              #0x66a1dc  ; [package:logger/src/logger.dart] Logger::d
    // 0x6c7d8c: r0 = Null
    //     0x6c7d8c: mov             x0, NULL
    // 0x6c7d90: r0 = ReturnAsyncNotFuture()
    //     0x6c7d90: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6c7d94: ldur            x0, [fp, #-0x90]
    // 0x6c7d98: r2 = LoadInt32Instr(r0)
    //     0x6c7d98: sbfx            x2, x0, #1, #0x1f
    //     0x6c7d9c: tbz             w0, #0, #0x6c7da4
    //     0x6c7da0: ldur            x2, [x0, #7]
    // 0x6c7da4: ldur            x1, [fp, #-0x88]
    // 0x6c7da8: ldur            x3, [fp, #-0x98]
    // 0x6c7dac: r0 = _performInitialization()
    //     0x6c7dac: bl              #0x6c7e14  ; [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_performInitialization
    // 0x6c7db0: mov             x1, x0
    // 0x6c7db4: stur            x1, [fp, #-0x90]
    // 0x6c7db8: r0 = Await()
    //     0x6c7db8: bl              #0x610dcc  ; AwaitStub
    // 0x6c7dbc: r0 = Null
    //     0x6c7dbc: mov             x0, NULL
    // 0x6c7dc0: r0 = ReturnAsyncNotFuture()
    //     0x6c7dc0: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6c7dc4: sub             SP, fp, #0xb8
    // 0x6c7dc8: mov             x3, x0
    // 0x6c7dcc: stur            x0, [fp, #-0x88]
    // 0x6c7dd0: mov             x0, x1
    // 0x6c7dd4: stur            x1, [fp, #-0x90]
    // 0x6c7dd8: ldur            x1, [fp, #-0x10]
    // 0x6c7ddc: ArrayLoad: r2 = r1[0]  ; List_4
    //     0x6c7ddc: ldur            w2, [x1, #0x17]
    // 0x6c7de0: DecompressPointer r2
    //     0x6c7de0: add             x2, x2, HEAP, lsl #32
    // 0x6c7de4: stp             x0, x3, [SP]
    // 0x6c7de8: mov             x1, x2
    // 0x6c7dec: r2 = "PosePluginManager: 初始化失败"
    //     0x6c7dec: add             x2, PP, #0xa, lsl #12  ; [pp+0xa5b0] "PosePluginManager: 初始化失败"
    //     0x6c7df0: ldr             x2, [x2, #0x5b0]
    // 0x6c7df4: r4 = const [0, 0x4, 0x2, 0x2, error, 0x2, stackTrace, 0x3, null]
    //     0x6c7df4: ldr             x4, [PP, #0x4240]  ; [pp+0x4240] List(9) [0, 0x4, 0x2, 0x2, "error", 0x2, "stackTrace", 0x3, Null]
    // 0x6c7df8: r0 = e()
    //     0x6c7df8: bl              #0x6615b8  ; [package:logger/src/logger.dart] Logger::e
    // 0x6c7dfc: ldur            x0, [fp, #-0x88]
    // 0x6c7e00: ldur            x1, [fp, #-0x90]
    // 0x6c7e04: r0 = ReThrow()
    //     0x6c7e04: bl              #0xf80898  ; ReThrowStub
    // 0x6c7e08: brk             #0
    // 0x6c7e0c: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c7e0c: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c7e10: b               #0x6c79b0
  }
  _ _performInitialization(/* No info */) async {
    // ** addr: 0x6c7e14, size: 0x2a4
    // 0x6c7e14: EnterFrame
    //     0x6c7e14: stp             fp, lr, [SP, #-0x10]!
    //     0x6c7e18: mov             fp, SP
    // 0x6c7e1c: AllocStack(0x50)
    //     0x6c7e1c: sub             SP, SP, #0x50
    // 0x6c7e20: SetupParameters(PosePluginManager this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */, dynamic _ /* r3 => r3, fp-0x20 */)
    //     0x6c7e20: stur            NULL, [fp, #-8]
    //     0x6c7e24: stur            x1, [fp, #-0x10]
    //     0x6c7e28: stur            x2, [fp, #-0x18]
    //     0x6c7e2c: stur            x3, [fp, #-0x20]
    // 0x6c7e30: CheckStackOverflow
    //     0x6c7e30: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c7e34: cmp             SP, x16
    //     0x6c7e38: b.ls            #0x6c80b0
    // 0x6c7e3c: InitAsync() -> Future<void?>
    //     0x6c7e3c: ldr             x0, [PP, #0x360]  ; [pp+0x360] TypeArguments: <void?>
    //     0x6c7e40: bl              #0x61100c  ; InitAsyncStub
    // 0x6c7e44: ldur            x0, [fp, #-0x10]
    // 0x6c7e48: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6c7e48: ldur            w3, [x0, #0x17]
    // 0x6c7e4c: DecompressPointer r3
    //     0x6c7e4c: add             x3, x3, HEAP, lsl #32
    // 0x6c7e50: stur            x3, [fp, #-0x28]
    // 0x6c7e54: r1 = Null
    //     0x6c7e54: mov             x1, NULL
    // 0x6c7e58: r2 = 10
    //     0x6c7e58: movz            x2, #0xa
    // 0x6c7e5c: r0 = AllocateArray()
    //     0x6c7e5c: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c7e60: r16 = "PosePluginManager: 开始"
    //     0x6c7e60: add             x16, PP, #0xa, lsl #12  ; [pp+0xa5b8] "PosePluginManager: 开始"
    //     0x6c7e64: ldr             x16, [x16, #0x5b8]
    // 0x6c7e68: StoreField: r0->field_f = r16
    //     0x6c7e68: stur            w16, [x0, #0xf]
    // 0x6c7e6c: ldur            x1, [fp, #-0x20]
    // 0x6c7e70: tbnz            w1, #4, #0x6c7e80
    // 0x6c7e74: r3 = "强制"
    //     0x6c7e74: add             x3, PP, #0xa, lsl #12  ; [pp+0xa5c0] "强制"
    //     0x6c7e78: ldr             x3, [x3, #0x5c0]
    // 0x6c7e7c: b               #0x6c7e88
    // 0x6c7e80: r3 = "智能"
    //     0x6c7e80: add             x3, PP, #0xa, lsl #12  ; [pp+0xa5c8] "智能"
    //     0x6c7e84: ldr             x3, [x3, #0x5c8]
    // 0x6c7e88: ldur            x2, [fp, #-0x18]
    // 0x6c7e8c: StoreField: r0->field_13 = r3
    //     0x6c7e8c: stur            w3, [x0, #0x13]
    // 0x6c7e90: r16 = "初始化 (maxPoses: "
    //     0x6c7e90: add             x16, PP, #0xa, lsl #12  ; [pp+0xa5d0] "初始化 (maxPoses: "
    //     0x6c7e94: ldr             x16, [x16, #0x5d0]
    // 0x6c7e98: ArrayStore: r0[0] = r16  ; List_4
    //     0x6c7e98: stur            w16, [x0, #0x17]
    // 0x6c7e9c: lsl             x3, x2, #1
    // 0x6c7ea0: stur            x3, [fp, #-0x30]
    // 0x6c7ea4: StoreField: r0->field_1b = r3
    //     0x6c7ea4: stur            w3, [x0, #0x1b]
    // 0x6c7ea8: r16 = ")"
    //     0x6c7ea8: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0x6c7eac: StoreField: r0->field_1f = r16
    //     0x6c7eac: stur            w16, [x0, #0x1f]
    // 0x6c7eb0: str             x0, [SP]
    // 0x6c7eb4: r0 = _interpolate()
    //     0x6c7eb4: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c7eb8: ldur            x1, [fp, #-0x28]
    // 0x6c7ebc: mov             x2, x0
    // 0x6c7ec0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7ec0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7ec4: r0 = i()
    //     0x6c7ec4: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c7ec8: r0 = LoadStaticField(0x1408)
    //     0x6c7ec8: ldr             x0, [THR, #0x68]  ; THR::field_table_values
    //     0x6c7ecc: ldr             x0, [x0, #0x2810]
    // 0x6c7ed0: tbz             w0, #4, #0x6c7edc
    // 0x6c7ed4: ldur            x0, [fp, #-0x20]
    // 0x6c7ed8: b               #0x6c7ee4
    // 0x6c7edc: ldur            x0, [fp, #-0x20]
    // 0x6c7ee0: tbnz            w0, #4, #0x6c7fb0
    // 0x6c7ee4: ldur            x2, [fp, #-0x10]
    // 0x6c7ee8: LoadField: r3 = r2->field_1b
    //     0x6c7ee8: ldur            w3, [x2, #0x1b]
    // 0x6c7eec: DecompressPointer r3
    //     0x6c7eec: add             x3, x3, HEAP, lsl #32
    // 0x6c7ef0: stur            x3, [fp, #-0x38]
    // 0x6c7ef4: ldur            x16, [fp, #-0x30]
    // 0x6c7ef8: stp             x0, x16, [SP]
    // 0x6c7efc: mov             x1, x3
    // 0x6c7f00: r4 = const [0, 0x3, 0x2, 0x1, forceReinitialize, 0x2, maxPoses, 0x1, null]
    //     0x6c7f00: add             x4, PP, #0xa, lsl #12  ; [pp+0xa588] List(9) [0, 0x3, 0x2, 0x1, "forceReinitialize", 0x2, "maxPoses", 0x1, Null]
    //     0x6c7f04: ldr             x4, [x4, #0x588]
    // 0x6c7f08: r0 = initialize()
    //     0x6c7f08: bl              #0x6c84f8  ; [package:keepdance/pose/utils/pose_plugin.dart] PosePlugin::initialize
    // 0x6c7f0c: mov             x1, x0
    // 0x6c7f10: stur            x1, [fp, #-0x40]
    // 0x6c7f14: r0 = Await()
    //     0x6c7f14: bl              #0x610dcc  ; AwaitStub
    // 0x6c7f18: ldur            x2, [fp, #-0x10]
    // 0x6c7f1c: r1 = Function '_handlePoseResult@1480436178':.
    //     0x6c7f1c: add             x1, PP, #0xa, lsl #12  ; [pp+0xa5d8] AnonymousClosure: (0x6c80b8), in [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_handlePoseResult (0x6c80f4)
    //     0x6c7f20: ldr             x1, [x1, #0x5d8]
    // 0x6c7f24: r0 = AllocateClosure()
    //     0x6c7f24: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c7f28: ldur            x1, [fp, #-0x38]
    // 0x6c7f2c: StoreField: r1->field_7 = r0
    //     0x6c7f2c: stur            w0, [x1, #7]
    //     0x6c7f30: ldurb           w16, [x1, #-1]
    //     0x6c7f34: ldurb           w17, [x0, #-1]
    //     0x6c7f38: and             x16, x17, x16, lsr #2
    //     0x6c7f3c: tst             x16, HEAP, lsr #32
    //     0x6c7f40: b.eq            #0x6c7f48
    //     0x6c7f44: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6c7f48: r0 = true
    //     0x6c7f48: add             x0, NULL, #0x20  ; true
    // 0x6c7f4c: StoreStaticField(0x1408, r0)
    //     0x6c7f4c: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x6c7f50: str             x0, [x1, #0x2810]
    // 0x6c7f54: r1 = Null
    //     0x6c7f54: mov             x1, NULL
    // 0x6c7f58: r2 = 6
    //     0x6c7f58: movz            x2, #0x6
    // 0x6c7f5c: r0 = AllocateArray()
    //     0x6c7f5c: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c7f60: r16 = "PosePluginManager: 已设置"
    //     0x6c7f60: add             x16, PP, #0xa, lsl #12  ; [pp+0xa5e0] "PosePluginManager: 已设置"
    //     0x6c7f64: ldr             x16, [x16, #0x5e0]
    // 0x6c7f68: StoreField: r0->field_f = r16
    //     0x6c7f68: stur            w16, [x0, #0xf]
    // 0x6c7f6c: ldur            x1, [fp, #-0x20]
    // 0x6c7f70: tbnz            w1, #4, #0x6c7f80
    // 0x6c7f74: r1 = "重新"
    //     0x6c7f74: add             x1, PP, #0xa, lsl #12  ; [pp+0xa5e8] "重新"
    //     0x6c7f78: ldr             x1, [x1, #0x5e8]
    // 0x6c7f7c: b               #0x6c7f84
    // 0x6c7f80: r1 = ""
    //     0x6c7f80: ldr             x1, [PP, #0xc0]  ; [pp+0xc0] ""
    // 0x6c7f84: StoreField: r0->field_13 = r1
    //     0x6c7f84: stur            w1, [x0, #0x13]
    // 0x6c7f88: r16 = "全局回调处理器"
    //     0x6c7f88: add             x16, PP, #0xa, lsl #12  ; [pp+0xa5f0] "全局回调处理器"
    //     0x6c7f8c: ldr             x16, [x16, #0x5f0]
    // 0x6c7f90: ArrayStore: r0[0] = r16  ; List_4
    //     0x6c7f90: stur            w16, [x0, #0x17]
    // 0x6c7f94: str             x0, [SP]
    // 0x6c7f98: r0 = _interpolate()
    //     0x6c7f98: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c7f9c: ldur            x1, [fp, #-0x28]
    // 0x6c7fa0: mov             x2, x0
    // 0x6c7fa4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7fa4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7fa8: r0 = i()
    //     0x6c7fa8: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c7fac: b               #0x6c7ff4
    // 0x6c7fb0: ldur            x0, [fp, #-0x10]
    // 0x6c7fb4: LoadField: r1 = r0->field_1b
    //     0x6c7fb4: ldur            w1, [x0, #0x1b]
    // 0x6c7fb8: DecompressPointer r1
    //     0x6c7fb8: add             x1, x1, HEAP, lsl #32
    // 0x6c7fbc: ldur            x16, [fp, #-0x30]
    // 0x6c7fc0: r30 = false
    //     0x6c7fc0: add             lr, NULL, #0x30  ; false
    // 0x6c7fc4: stp             lr, x16, [SP]
    // 0x6c7fc8: r4 = const [0, 0x3, 0x2, 0x1, forceReinitialize, 0x2, maxPoses, 0x1, null]
    //     0x6c7fc8: add             x4, PP, #0xa, lsl #12  ; [pp+0xa588] List(9) [0, 0x3, 0x2, 0x1, "forceReinitialize", 0x2, "maxPoses", 0x1, Null]
    //     0x6c7fcc: ldr             x4, [x4, #0x588]
    // 0x6c7fd0: r0 = initialize()
    //     0x6c7fd0: bl              #0x6c84f8  ; [package:keepdance/pose/utils/pose_plugin.dart] PosePlugin::initialize
    // 0x6c7fd4: mov             x1, x0
    // 0x6c7fd8: stur            x1, [fp, #-0x20]
    // 0x6c7fdc: r0 = Await()
    //     0x6c7fdc: bl              #0x610dcc  ; AwaitStub
    // 0x6c7fe0: ldur            x1, [fp, #-0x28]
    // 0x6c7fe4: r2 = "PosePluginManager: 已更新检测器参数"
    //     0x6c7fe4: add             x2, PP, #0xa, lsl #12  ; [pp+0xa5f8] "PosePluginManager: 已更新检测器参数"
    //     0x6c7fe8: ldr             x2, [x2, #0x5f8]
    // 0x6c7fec: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c7fec: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c7ff0: r0 = i()
    //     0x6c7ff0: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c7ff4: ldur            x0, [fp, #-0x10]
    // 0x6c7ff8: ldur            x2, [fp, #-0x18]
    // 0x6c7ffc: ldur            x3, [fp, #-0x30]
    // 0x6c8000: r1 = true
    //     0x6c8000: add             x1, NULL, #0x20  ; true
    // 0x6c8004: StoreField: r0->field_23 = r1
    //     0x6c8004: stur            w1, [x0, #0x23]
    // 0x6c8008: StoreField: r0->field_27 = r2
    //     0x6c8008: stur            x2, [x0, #0x27]
    // 0x6c800c: r0 = DateTime()
    //     0x6c800c: bl              #0x6129ac  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x6c8010: mov             x1, x0
    // 0x6c8014: r0 = false
    //     0x6c8014: add             x0, NULL, #0x30  ; false
    // 0x6c8018: stur            x1, [fp, #-0x20]
    // 0x6c801c: StoreField: r1->field_13 = r0
    //     0x6c801c: stur            w0, [x1, #0x13]
    // 0x6c8020: r0 = _getCurrentMicros()
    //     0x6c8020: bl              #0x612930  ; [dart:core] DateTime::_getCurrentMicros
    // 0x6c8024: r1 = LoadInt32Instr(r0)
    //     0x6c8024: sbfx            x1, x0, #1, #0x1f
    //     0x6c8028: tbz             w0, #0, #0x6c8030
    //     0x6c802c: ldur            x1, [x0, #7]
    // 0x6c8030: ldur            x0, [fp, #-0x20]
    // 0x6c8034: StoreField: r0->field_7 = r1
    //     0x6c8034: stur            x1, [x0, #7]
    // 0x6c8038: ldur            x1, [fp, #-0x10]
    // 0x6c803c: StoreField: r1->field_2f = r0
    //     0x6c803c: stur            w0, [x1, #0x2f]
    //     0x6c8040: ldurb           w16, [x1, #-1]
    //     0x6c8044: ldurb           w17, [x0, #-1]
    //     0x6c8048: and             x16, x17, x16, lsr #2
    //     0x6c804c: tst             x16, HEAP, lsr #32
    //     0x6c8050: b.eq            #0x6c8058
    //     0x6c8054: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6c8058: LoadField: r0 = r1->field_33
    //     0x6c8058: ldur            w0, [x1, #0x33]
    // 0x6c805c: DecompressPointer r0
    //     0x6c805c: add             x0, x0, HEAP, lsl #32
    // 0x6c8060: mov             x1, x0
    // 0x6c8064: r0 = clear()
    //     0x6c8064: bl              #0x785e80  ; [dart:core] _GrowableList::clear
    // 0x6c8068: r1 = Null
    //     0x6c8068: mov             x1, NULL
    // 0x6c806c: r2 = 6
    //     0x6c806c: movz            x2, #0x6
    // 0x6c8070: r0 = AllocateArray()
    //     0x6c8070: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c8074: r16 = "PosePluginManager: 初始化完成 (maxPoses: "
    //     0x6c8074: add             x16, PP, #0xa, lsl #12  ; [pp+0xa600] "PosePluginManager: 初始化完成 (maxPoses: "
    //     0x6c8078: ldr             x16, [x16, #0x600]
    // 0x6c807c: StoreField: r0->field_f = r16
    //     0x6c807c: stur            w16, [x0, #0xf]
    // 0x6c8080: ldur            x1, [fp, #-0x30]
    // 0x6c8084: StoreField: r0->field_13 = r1
    //     0x6c8084: stur            w1, [x0, #0x13]
    // 0x6c8088: r16 = ")"
    //     0x6c8088: ldr             x16, [PP, #0xd30]  ; [pp+0xd30] ")"
    // 0x6c808c: ArrayStore: r0[0] = r16  ; List_4
    //     0x6c808c: stur            w16, [x0, #0x17]
    // 0x6c8090: str             x0, [SP]
    // 0x6c8094: r0 = _interpolate()
    //     0x6c8094: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c8098: ldur            x1, [fp, #-0x28]
    // 0x6c809c: mov             x2, x0
    // 0x6c80a0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c80a0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c80a4: r0 = i()
    //     0x6c80a4: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c80a8: r0 = Null
    //     0x6c80a8: mov             x0, NULL
    // 0x6c80ac: r0 = ReturnAsyncNotFuture()
    //     0x6c80ac: b               #0x610b58  ; ReturnAsyncNotFutureStub
    // 0x6c80b0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c80b0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c80b4: b               #0x6c7e3c
  }
  [closure] void _handlePoseResult(dynamic, List<List<PoseJoint>>) {
    // ** addr: 0x6c80b8, size: 0x3c
    // 0x6c80b8: EnterFrame
    //     0x6c80b8: stp             fp, lr, [SP, #-0x10]!
    //     0x6c80bc: mov             fp, SP
    // 0x6c80c0: ldr             x0, [fp, #0x18]
    // 0x6c80c4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6c80c4: ldur            w1, [x0, #0x17]
    // 0x6c80c8: DecompressPointer r1
    //     0x6c80c8: add             x1, x1, HEAP, lsl #32
    // 0x6c80cc: CheckStackOverflow
    //     0x6c80cc: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c80d0: cmp             SP, x16
    //     0x6c80d4: b.ls            #0x6c80ec
    // 0x6c80d8: ldr             x2, [fp, #0x10]
    // 0x6c80dc: r0 = _handlePoseResult()
    //     0x6c80dc: bl              #0x6c80f4  ; [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_handlePoseResult
    // 0x6c80e0: LeaveFrame
    //     0x6c80e0: mov             SP, fp
    //     0x6c80e4: ldp             fp, lr, [SP], #0x10
    // 0x6c80e8: ret
    //     0x6c80e8: ret             
    // 0x6c80ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c80ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c80f0: b               #0x6c80d8
  }
  _ _handlePoseResult(/* No info */) {
    // ** addr: 0x6c80f4, size: 0x88
    // 0x6c80f4: EnterFrame
    //     0x6c80f4: stp             fp, lr, [SP, #-0x10]!
    //     0x6c80f8: mov             fp, SP
    // 0x6c80fc: AllocStack(0x20)
    //     0x6c80fc: sub             SP, SP, #0x20
    // 0x6c8100: SetupParameters(PosePluginManager this /* r1 => r1, fp-0x10 */, dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x6c8100: stur            x1, [fp, #-0x10]
    //     0x6c8104: stur            x2, [fp, #-0x18]
    // 0x6c8108: CheckStackOverflow
    //     0x6c8108: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c810c: cmp             SP, x16
    //     0x6c8110: b.ls            #0x6c8174
    // 0x6c8114: LoadField: r3 = r1->field_1f
    //     0x6c8114: ldur            w3, [x1, #0x1f]
    // 0x6c8118: DecompressPointer r3
    //     0x6c8118: add             x3, x3, HEAP, lsl #32
    // 0x6c811c: stur            x3, [fp, #-8]
    // 0x6c8120: LoadField: r0 = r3->field_13
    //     0x6c8120: ldur            x0, [x3, #0x13]
    // 0x6c8124: tbnz            w0, #2, #0x6c8164
    // 0x6c8128: r0 = LoadClassIdInstr(r2)
    //     0x6c8128: ldur            x0, [x2, #-1]
    //     0x6c812c: ubfx            x0, x0, #0xc, #0x14
    // 0x6c8130: str             x2, [SP]
    // 0x6c8134: r0 = GDT[cid_x0 + 0xb092]()
    //     0x6c8134: movz            x17, #0xb092
    //     0x6c8138: add             lr, x0, x17
    //     0x6c813c: ldr             lr, [x21, lr, lsl #3]
    //     0x6c8140: blr             lr
    // 0x6c8144: r2 = LoadInt32Instr(r0)
    //     0x6c8144: sbfx            x2, x0, #1, #0x1f
    //     0x6c8148: tbz             w0, #0, #0x6c8150
    //     0x6c814c: ldur            x2, [x0, #7]
    // 0x6c8150: ldur            x1, [fp, #-0x10]
    // 0x6c8154: r0 = _updateDetectionQuality()
    //     0x6c8154: bl              #0x6c817c  ; [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_updateDetectionQuality
    // 0x6c8158: ldur            x1, [fp, #-8]
    // 0x6c815c: ldur            x2, [fp, #-0x18]
    // 0x6c8160: r0 = add()
    //     0x6c8160: bl              #0x5f7428  ; [dart:async] _BroadcastStreamController::add
    // 0x6c8164: r0 = Null
    //     0x6c8164: mov             x0, NULL
    // 0x6c8168: LeaveFrame
    //     0x6c8168: mov             SP, fp
    //     0x6c816c: ldp             fp, lr, [SP], #0x10
    // 0x6c8170: ret
    //     0x6c8170: ret             
    // 0x6c8174: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c8174: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c8178: b               #0x6c8114
  }
  _ _updateDetectionQuality(/* No info */) {
    // ** addr: 0x6c817c, size: 0xf4
    // 0x6c817c: EnterFrame
    //     0x6c817c: stp             fp, lr, [SP, #-0x10]!
    //     0x6c8180: mov             fp, SP
    // 0x6c8184: AllocStack(0x18)
    //     0x6c8184: sub             SP, SP, #0x18
    // 0x6c8188: SetupParameters(dynamic _ /* r2 => r2, fp-0x18 */)
    //     0x6c8188: stur            x2, [fp, #-0x18]
    // 0x6c818c: CheckStackOverflow
    //     0x6c818c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c8190: cmp             SP, x16
    //     0x6c8194: b.ls            #0x6c8264
    // 0x6c8198: LoadField: r0 = r1->field_33
    //     0x6c8198: ldur            w0, [x1, #0x33]
    // 0x6c819c: DecompressPointer r0
    //     0x6c819c: add             x0, x0, HEAP, lsl #32
    // 0x6c81a0: stur            x0, [fp, #-0x10]
    // 0x6c81a4: LoadField: r1 = r0->field_b
    //     0x6c81a4: ldur            w1, [x0, #0xb]
    // 0x6c81a8: LoadField: r3 = r0->field_f
    //     0x6c81a8: ldur            w3, [x0, #0xf]
    // 0x6c81ac: DecompressPointer r3
    //     0x6c81ac: add             x3, x3, HEAP, lsl #32
    // 0x6c81b0: LoadField: r4 = r3->field_b
    //     0x6c81b0: ldur            w4, [x3, #0xb]
    // 0x6c81b4: r3 = LoadInt32Instr(r1)
    //     0x6c81b4: sbfx            x3, x1, #1, #0x1f
    // 0x6c81b8: stur            x3, [fp, #-8]
    // 0x6c81bc: r1 = LoadInt32Instr(r4)
    //     0x6c81bc: sbfx            x1, x4, #1, #0x1f
    // 0x6c81c0: cmp             x3, x1
    // 0x6c81c4: b.ne            #0x6c81d0
    // 0x6c81c8: mov             x1, x0
    // 0x6c81cc: r0 = _growToNextCapacity()
    //     0x6c81cc: bl              #0x602aa4  ; [dart:core] _GrowableList::_growToNextCapacity
    // 0x6c81d0: ldur            x2, [fp, #-0x18]
    // 0x6c81d4: ldur            x3, [fp, #-0x10]
    // 0x6c81d8: ldur            x4, [fp, #-8]
    // 0x6c81dc: add             x5, x4, #1
    // 0x6c81e0: lsl             x0, x5, #1
    // 0x6c81e4: StoreField: r3->field_b = r0
    //     0x6c81e4: stur            w0, [x3, #0xb]
    // 0x6c81e8: mov             x0, x5
    // 0x6c81ec: mov             x1, x4
    // 0x6c81f0: cmp             x1, x0
    // 0x6c81f4: b.hs            #0x6c826c
    // 0x6c81f8: LoadField: r6 = r3->field_f
    //     0x6c81f8: ldur            w6, [x3, #0xf]
    // 0x6c81fc: DecompressPointer r6
    //     0x6c81fc: add             x6, x6, HEAP, lsl #32
    // 0x6c8200: r0 = BoxInt64Instr(r2)
    //     0x6c8200: sbfiz           x0, x2, #1, #0x1f
    //     0x6c8204: cmp             x2, x0, asr #1
    //     0x6c8208: b.eq            #0x6c8214
    //     0x6c820c: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c8210: stur            x2, [x0, #7]
    // 0x6c8214: mov             x1, x6
    // 0x6c8218: ArrayStore: r1[r4] = r0  ; List_4
    //     0x6c8218: add             x25, x1, x4, lsl #2
    //     0x6c821c: add             x25, x25, #0xf
    //     0x6c8220: str             w0, [x25]
    //     0x6c8224: tbz             w0, #0, #0x6c8240
    //     0x6c8228: ldurb           w16, [x1, #-1]
    //     0x6c822c: ldurb           w17, [x0, #-1]
    //     0x6c8230: and             x16, x17, x16, lsr #2
    //     0x6c8234: tst             x16, HEAP, lsr #32
    //     0x6c8238: b.eq            #0x6c8240
    //     0x6c823c: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6c8240: cmp             x5, #0xa
    // 0x6c8244: b.le            #0x6c8254
    // 0x6c8248: mov             x1, x3
    // 0x6c824c: r2 = 0
    //     0x6c824c: movz            x2, #0
    // 0x6c8250: r0 = removeAt()
    //     0x6c8250: bl              #0x77cdf8  ; [dart:core] _GrowableList::removeAt
    // 0x6c8254: r0 = Null
    //     0x6c8254: mov             x0, NULL
    // 0x6c8258: LeaveFrame
    //     0x6c8258: mov             SP, fp
    //     0x6c825c: ldp             fp, lr, [SP], #0x10
    // 0x6c8260: ret
    //     0x6c8260: ret             
    // 0x6c8264: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c8264: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c8268: b               #0x6c8198
    // 0x6c826c: r0 = RangeErrorSharedWithoutFPURegs()
    //     0x6c826c: bl              #0xf82c64  ; RangeErrorSharedWithoutFPURegsStub
  }
  _ _shouldReinitialize(/* No info */) {
    // ** addr: 0x6c8328, size: 0x1d0
    // 0x6c8328: EnterFrame
    //     0x6c8328: stp             fp, lr, [SP, #-0x10]!
    //     0x6c832c: mov             fp, SP
    // 0x6c8330: AllocStack(0x20)
    //     0x6c8330: sub             SP, SP, #0x20
    // 0x6c8334: SetupParameters(PosePluginManager this /* r1 => r1, fp-0x8 */)
    //     0x6c8334: stur            x1, [fp, #-8]
    // 0x6c8338: CheckStackOverflow
    //     0x6c8338: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c833c: cmp             SP, x16
    //     0x6c8340: b.ls            #0x6c84ec
    // 0x6c8344: tbnz            w2, #4, #0x6c8374
    // 0x6c8348: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x6c8348: ldur            w0, [x1, #0x17]
    // 0x6c834c: DecompressPointer r0
    //     0x6c834c: add             x0, x0, HEAP, lsl #32
    // 0x6c8350: mov             x1, x0
    // 0x6c8354: r2 = "PosePluginManager: 强制重新初始化"
    //     0x6c8354: add             x2, PP, #0xa, lsl #12  ; [pp+0xa608] "PosePluginManager: 强制重新初始化"
    //     0x6c8358: ldr             x2, [x2, #0x608]
    // 0x6c835c: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c835c: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c8360: r0 = i()
    //     0x6c8360: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c8364: r0 = true
    //     0x6c8364: add             x0, NULL, #0x20  ; true
    // 0x6c8368: LeaveFrame
    //     0x6c8368: mov             SP, fp
    //     0x6c836c: ldp             fp, lr, [SP], #0x10
    // 0x6c8370: ret
    //     0x6c8370: ret             
    // 0x6c8374: LoadField: r0 = r1->field_23
    //     0x6c8374: ldur            w0, [x1, #0x23]
    // 0x6c8378: DecompressPointer r0
    //     0x6c8378: add             x0, x0, HEAP, lsl #32
    // 0x6c837c: tbz             w0, #4, #0x6c83ac
    // 0x6c8380: ArrayLoad: r0 = r1[0]  ; List_4
    //     0x6c8380: ldur            w0, [x1, #0x17]
    // 0x6c8384: DecompressPointer r0
    //     0x6c8384: add             x0, x0, HEAP, lsl #32
    // 0x6c8388: mov             x1, x0
    // 0x6c838c: r2 = "PosePluginManager: 首次初始化"
    //     0x6c838c: add             x2, PP, #0xa, lsl #12  ; [pp+0xa610] "PosePluginManager: 首次初始化"
    //     0x6c8390: ldr             x2, [x2, #0x610]
    // 0x6c8394: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c8394: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c8398: r0 = i()
    //     0x6c8398: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x6c839c: r0 = true
    //     0x6c839c: add             x0, NULL, #0x20  ; true
    // 0x6c83a0: LeaveFrame
    //     0x6c83a0: mov             SP, fp
    //     0x6c83a4: ldp             fp, lr, [SP], #0x10
    // 0x6c83a8: ret
    //     0x6c83a8: ret             
    // 0x6c83ac: LoadField: r0 = r1->field_2f
    //     0x6c83ac: ldur            w0, [x1, #0x2f]
    // 0x6c83b0: DecompressPointer r0
    //     0x6c83b0: add             x0, x0, HEAP, lsl #32
    // 0x6c83b4: cmp             w0, NULL
    // 0x6c83b8: b.eq            #0x6c84a0
    // 0x6c83bc: r0 = DateTime()
    //     0x6c83bc: bl              #0x6129ac  ; AllocateDateTimeStub -> DateTime (size=0x18)
    // 0x6c83c0: mov             x1, x0
    // 0x6c83c4: r0 = false
    //     0x6c83c4: add             x0, NULL, #0x30  ; false
    // 0x6c83c8: stur            x1, [fp, #-0x10]
    // 0x6c83cc: StoreField: r1->field_13 = r0
    //     0x6c83cc: stur            w0, [x1, #0x13]
    // 0x6c83d0: r0 = _getCurrentMicros()
    //     0x6c83d0: bl              #0x612930  ; [dart:core] DateTime::_getCurrentMicros
    // 0x6c83d4: r1 = LoadInt32Instr(r0)
    //     0x6c83d4: sbfx            x1, x0, #1, #0x1f
    //     0x6c83d8: tbz             w0, #0, #0x6c83e0
    //     0x6c83dc: ldur            x1, [x0, #7]
    // 0x6c83e0: ldur            x0, [fp, #-0x10]
    // 0x6c83e4: StoreField: r0->field_7 = r1
    //     0x6c83e4: stur            x1, [x0, #7]
    // 0x6c83e8: ldur            x3, [fp, #-8]
    // 0x6c83ec: LoadField: r2 = r3->field_2f
    //     0x6c83ec: ldur            w2, [x3, #0x2f]
    // 0x6c83f0: DecompressPointer r2
    //     0x6c83f0: add             x2, x2, HEAP, lsl #32
    // 0x6c83f4: cmp             w2, NULL
    // 0x6c83f8: b.eq            #0x6c84f4
    // 0x6c83fc: mov             x1, x0
    // 0x6c8400: r0 = -()
    //     0x6c8400: bl              #0x6128fc  ; [dart:core] Duration::-
    // 0x6c8404: LoadField: r1 = r0->field_7
    //     0x6c8404: ldur            x1, [x0, #7]
    // 0x6c8408: r0 = 1000
    //     0x6c8408: movz            x0, #0x3e8
    // 0x6c840c: sdiv            x3, x1, x0
    // 0x6c8410: stur            x3, [fp, #-0x18]
    // 0x6c8414: cmp             x3, #0x3e8
    // 0x6c8418: b.ge            #0x6c8498
    // 0x6c841c: ldur            x0, [fp, #-8]
    // 0x6c8420: ArrayLoad: r4 = r0[0]  ; List_4
    //     0x6c8420: ldur            w4, [x0, #0x17]
    // 0x6c8424: DecompressPointer r4
    //     0x6c8424: add             x4, x4, HEAP, lsl #32
    // 0x6c8428: stur            x4, [fp, #-0x10]
    // 0x6c842c: r1 = Null
    //     0x6c842c: mov             x1, NULL
    // 0x6c8430: r2 = 6
    //     0x6c8430: movz            x2, #0x6
    // 0x6c8434: r0 = AllocateArray()
    //     0x6c8434: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c8438: mov             x2, x0
    // 0x6c843c: r16 = "PosePluginManager: 防抖机制阻止重复初始化 (距离上次"
    //     0x6c843c: add             x16, PP, #0xa, lsl #12  ; [pp+0xa618] "PosePluginManager: 防抖机制阻止重复初始化 (距离上次"
    //     0x6c8440: ldr             x16, [x16, #0x618]
    // 0x6c8444: StoreField: r2->field_f = r16
    //     0x6c8444: stur            w16, [x2, #0xf]
    // 0x6c8448: ldur            x3, [fp, #-0x18]
    // 0x6c844c: r0 = BoxInt64Instr(r3)
    //     0x6c844c: sbfiz           x0, x3, #1, #0x1f
    //     0x6c8450: cmp             x3, x0, asr #1
    //     0x6c8454: b.eq            #0x6c8460
    //     0x6c8458: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c845c: stur            x3, [x0, #7]
    // 0x6c8460: StoreField: r2->field_13 = r0
    //     0x6c8460: stur            w0, [x2, #0x13]
    // 0x6c8464: r16 = "ms)"
    //     0x6c8464: add             x16, PP, #0xa, lsl #12  ; [pp+0xa620] "ms)"
    //     0x6c8468: ldr             x16, [x16, #0x620]
    // 0x6c846c: ArrayStore: r2[0] = r16  ; List_4
    //     0x6c846c: stur            w16, [x2, #0x17]
    // 0x6c8470: str             x2, [SP]
    // 0x6c8474: r0 = _interpolate()
    //     0x6c8474: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c8478: ldur            x1, [fp, #-0x10]
    // 0x6c847c: mov             x2, x0
    // 0x6c8480: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c8480: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c8484: r0 = w()
    //     0x6c8484: bl              #0x61c68c  ; [package:logger/src/logger.dart] Logger::w
    // 0x6c8488: r0 = false
    //     0x6c8488: add             x0, NULL, #0x30  ; false
    // 0x6c848c: LeaveFrame
    //     0x6c848c: mov             SP, fp
    //     0x6c8490: ldp             fp, lr, [SP], #0x10
    // 0x6c8494: ret
    //     0x6c8494: ret             
    // 0x6c8498: ldur            x0, [fp, #-8]
    // 0x6c849c: b               #0x6c84a4
    // 0x6c84a0: mov             x0, x1
    // 0x6c84a4: mov             x1, x0
    // 0x6c84a8: r0 = _isDetectionQualityGood()
    //     0x6c84a8: bl              #0x6c9868  ; [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_isDetectionQualityGood
    // 0x6c84ac: tbz             w0, #4, #0x6c84dc
    // 0x6c84b0: ldur            x0, [fp, #-8]
    // 0x6c84b4: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x6c84b4: ldur            w1, [x0, #0x17]
    // 0x6c84b8: DecompressPointer r1
    //     0x6c84b8: add             x1, x1, HEAP, lsl #32
    // 0x6c84bc: r2 = "PosePluginManager: 检测质量异常，允许重新初始化"
    //     0x6c84bc: add             x2, PP, #0xa, lsl #12  ; [pp+0xa628] "PosePluginManager: 检测质量异常，允许重新初始化"
    //     0x6c84c0: ldr             x2, [x2, #0x628]
    // 0x6c84c4: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c84c4: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c84c8: r0 = w()
    //     0x6c84c8: bl              #0x61c68c  ; [package:logger/src/logger.dart] Logger::w
    // 0x6c84cc: r0 = true
    //     0x6c84cc: add             x0, NULL, #0x20  ; true
    // 0x6c84d0: LeaveFrame
    //     0x6c84d0: mov             SP, fp
    //     0x6c84d4: ldp             fp, lr, [SP], #0x10
    // 0x6c84d8: ret
    //     0x6c84d8: ret             
    // 0x6c84dc: r0 = false
    //     0x6c84dc: add             x0, NULL, #0x30  ; false
    // 0x6c84e0: LeaveFrame
    //     0x6c84e0: mov             SP, fp
    //     0x6c84e4: ldp             fp, lr, [SP], #0x10
    // 0x6c84e8: ret
    //     0x6c84e8: ret             
    // 0x6c84ec: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c84ec: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c84f0: b               #0x6c8344
    // 0x6c84f4: r0 = NullCastErrorSharedWithoutFPURegs()
    //     0x6c84f4: bl              #0xf82dc4  ; NullCastErrorSharedWithoutFPURegsStub
  }
  _ _shouldAllowDowngrade(/* No info */) {
    // ** addr: 0x6c9770, size: 0xac
    // 0x6c9770: EnterFrame
    //     0x6c9770: stp             fp, lr, [SP, #-0x10]!
    //     0x6c9774: mov             fp, SP
    // 0x6c9778: AllocStack(0x8)
    //     0x6c9778: sub             SP, SP, #8
    // 0x6c977c: CheckStackOverflow
    //     0x6c977c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c9780: cmp             SP, x16
    //     0x6c9784: b.ls            #0x6c9814
    // 0x6c9788: LoadField: r0 = r1->field_33
    //     0x6c9788: ldur            w0, [x1, #0x33]
    // 0x6c978c: DecompressPointer r0
    //     0x6c978c: add             x0, x0, HEAP, lsl #32
    // 0x6c9790: LoadField: r1 = r0->field_b
    //     0x6c9790: ldur            w1, [x0, #0xb]
    // 0x6c9794: r2 = LoadInt32Instr(r1)
    //     0x6c9794: sbfx            x2, x1, #1, #0x1f
    // 0x6c9798: cmp             x2, #5
    // 0x6c979c: b.lt            #0x6c9804
    // 0x6c97a0: mov             x1, x0
    // 0x6c97a4: r2 = 5
    //     0x6c97a4: movz            x2, #0x5
    // 0x6c97a8: r0 = take()
    //     0x6c97a8: bl              #0x9e3eb8  ; [dart:collection] ListBase::take
    // 0x6c97ac: r1 = Function '<anonymous closure>':.
    //     0x6c97ac: add             x1, PP, #0xa, lsl #12  ; [pp+0xa7b0] AnonymousClosure: (0x6c981c), in [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_shouldAllowDowngrade (0x6c9770)
    //     0x6c97b0: ldr             x1, [x1, #0x7b0]
    // 0x6c97b4: r2 = Null
    //     0x6c97b4: mov             x2, NULL
    // 0x6c97b8: stur            x0, [fp, #-8]
    // 0x6c97bc: r0 = AllocateClosure()
    //     0x6c97bc: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c97c0: ldur            x1, [fp, #-8]
    // 0x6c97c4: mov             x2, x0
    // 0x6c97c8: r0 = reduce()
    //     0x6c97c8: bl              #0x82fc3c  ; [dart:_internal] ListIterable::reduce
    // 0x6c97cc: r1 = LoadInt32Instr(r0)
    //     0x6c97cc: sbfx            x1, x0, #1, #0x1f
    //     0x6c97d0: tbz             w0, #0, #0x6c97d8
    //     0x6c97d4: ldur            x1, [x0, #7]
    // 0x6c97d8: scvtf           d0, x1
    // 0x6c97dc: d1 = 5.000000
    //     0x6c97dc: fmov            d1, #5.00000000
    // 0x6c97e0: fdiv            d2, d0, d1
    // 0x6c97e4: d0 = 1.500000
    //     0x6c97e4: fmov            d0, #1.50000000
    // 0x6c97e8: fcmp            d0, d2
    // 0x6c97ec: r16 = true
    //     0x6c97ec: add             x16, NULL, #0x20  ; true
    // 0x6c97f0: r17 = false
    //     0x6c97f0: add             x17, NULL, #0x30  ; false
    // 0x6c97f4: csel            x0, x16, x17, gt
    // 0x6c97f8: LeaveFrame
    //     0x6c97f8: mov             SP, fp
    //     0x6c97fc: ldp             fp, lr, [SP], #0x10
    // 0x6c9800: ret
    //     0x6c9800: ret             
    // 0x6c9804: r0 = false
    //     0x6c9804: add             x0, NULL, #0x30  ; false
    // 0x6c9808: LeaveFrame
    //     0x6c9808: mov             SP, fp
    //     0x6c980c: ldp             fp, lr, [SP], #0x10
    // 0x6c9810: ret
    //     0x6c9810: ret             
    // 0x6c9814: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c9814: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c9818: b               #0x6c9788
  }
  [closure] int <anonymous closure>(dynamic, int, int) {
    // ** addr: 0x6c981c, size: 0x4c
    // 0x6c981c: ldr             x2, [SP, #8]
    // 0x6c9820: r3 = LoadInt32Instr(r2)
    //     0x6c9820: sbfx            x3, x2, #1, #0x1f
    //     0x6c9824: tbz             w2, #0, #0x6c982c
    //     0x6c9828: ldur            x3, [x2, #7]
    // 0x6c982c: ldr             x2, [SP]
    // 0x6c9830: r4 = LoadInt32Instr(r2)
    //     0x6c9830: sbfx            x4, x2, #1, #0x1f
    //     0x6c9834: tbz             w2, #0, #0x6c983c
    //     0x6c9838: ldur            x4, [x2, #7]
    // 0x6c983c: add             x2, x3, x4
    // 0x6c9840: r0 = BoxInt64Instr(r2)
    //     0x6c9840: sbfiz           x0, x2, #1, #0x1f
    //     0x6c9844: cmp             x2, x0, asr #1
    //     0x6c9848: b.eq            #0x6c9864
    //     0x6c984c: stp             fp, lr, [SP, #-0x10]!
    //     0x6c9850: mov             fp, SP
    //     0x6c9854: bl              #0xf8299c  ; AllocateMintSharedWithoutFPURegsStub
    //     0x6c9858: mov             SP, fp
    //     0x6c985c: ldp             fp, lr, [SP], #0x10
    //     0x6c9860: stur            x2, [x0, #7]
    // 0x6c9864: ret
    //     0x6c9864: ret             
  }
  _ _isDetectionQualityGood(/* No info */) {
    // ** addr: 0x6c9868, size: 0x254
    // 0x6c9868: EnterFrame
    //     0x6c9868: stp             fp, lr, [SP, #-0x10]!
    //     0x6c986c: mov             fp, SP
    // 0x6c9870: AllocStack(0x30)
    //     0x6c9870: sub             SP, SP, #0x30
    // 0x6c9874: SetupParameters(PosePluginManager this /* r1 => r0, fp-0x10 */)
    //     0x6c9874: mov             x0, x1
    //     0x6c9878: stur            x1, [fp, #-0x10]
    // 0x6c987c: CheckStackOverflow
    //     0x6c987c: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c9880: cmp             SP, x16
    //     0x6c9884: b.ls            #0x6c9a98
    // 0x6c9888: LoadField: r3 = r0->field_33
    //     0x6c9888: ldur            w3, [x0, #0x33]
    // 0x6c988c: DecompressPointer r3
    //     0x6c988c: add             x3, x3, HEAP, lsl #32
    // 0x6c9890: stur            x3, [fp, #-8]
    // 0x6c9894: LoadField: r1 = r3->field_b
    //     0x6c9894: ldur            w1, [x3, #0xb]
    // 0x6c9898: cbnz            w1, #0x6c98ac
    // 0x6c989c: r0 = true
    //     0x6c989c: add             x0, NULL, #0x20  ; true
    // 0x6c98a0: LeaveFrame
    //     0x6c98a0: mov             SP, fp
    //     0x6c98a4: ldp             fp, lr, [SP], #0x10
    // 0x6c98a8: ret
    //     0x6c98a8: ret             
    // 0x6c98ac: mov             x1, x3
    // 0x6c98b0: r2 = 5
    //     0x6c98b0: movz            x2, #0x5
    // 0x6c98b4: r0 = take()
    //     0x6c98b4: bl              #0x9e3eb8  ; [dart:collection] ListBase::take
    // 0x6c98b8: r1 = Function '<anonymous closure>':.
    //     0x6c98b8: add             x1, PP, #0xa, lsl #12  ; [pp+0xa7d0] AnonymousClosure: (0x6c9c24), in [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_isDetectionQualityGood (0x6c9868)
    //     0x6c98bc: ldr             x1, [x1, #0x7d0]
    // 0x6c98c0: r2 = Null
    //     0x6c98c0: mov             x2, NULL
    // 0x6c98c4: stur            x0, [fp, #-0x18]
    // 0x6c98c8: r0 = AllocateClosure()
    //     0x6c98c8: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c98cc: ldur            x1, [fp, #-0x18]
    // 0x6c98d0: mov             x2, x0
    // 0x6c98d4: r0 = where()
    //     0x6c98d4: bl              #0x9b3e44  ; [dart:collection] __Set&_HashVMBase&SetMixin::where
    // 0x6c98d8: str             x0, [SP]
    // 0x6c98dc: r0 = length()
    //     0x6c98dc: bl              #0x9e1908  ; [dart:core] Iterable::length
    // 0x6c98e0: stur            x0, [fp, #-0x20]
    // 0x6c98e4: r1 = LoadInt32Instr(r0)
    //     0x6c98e4: sbfx            x1, x0, #1, #0x1f
    //     0x6c98e8: tbz             w0, #0, #0x6c98f0
    //     0x6c98ec: ldur            x1, [x0, #7]
    // 0x6c98f0: cmp             x1, #3
    // 0x6c98f4: b.lt            #0x6c995c
    // 0x6c98f8: ldur            x3, [fp, #-0x10]
    // 0x6c98fc: ArrayLoad: r4 = r3[0]  ; List_4
    //     0x6c98fc: ldur            w4, [x3, #0x17]
    // 0x6c9900: DecompressPointer r4
    //     0x6c9900: add             x4, x4, HEAP, lsl #32
    // 0x6c9904: stur            x4, [fp, #-0x18]
    // 0x6c9908: r1 = Null
    //     0x6c9908: mov             x1, NULL
    // 0x6c990c: r2 = 6
    //     0x6c990c: movz            x2, #0x6
    // 0x6c9910: r0 = AllocateArray()
    //     0x6c9910: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c9914: r16 = "PosePluginManager: 检测质量异常，最近5次检测中有"
    //     0x6c9914: add             x16, PP, #0xa, lsl #12  ; [pp+0xa7d8] "PosePluginManager: 检测质量异常，最近5次检测中有"
    //     0x6c9918: ldr             x16, [x16, #0x7d8]
    // 0x6c991c: StoreField: r0->field_f = r16
    //     0x6c991c: stur            w16, [x0, #0xf]
    // 0x6c9920: ldur            x1, [fp, #-0x20]
    // 0x6c9924: StoreField: r0->field_13 = r1
    //     0x6c9924: stur            w1, [x0, #0x13]
    // 0x6c9928: r16 = "次为0"
    //     0x6c9928: add             x16, PP, #0xa, lsl #12  ; [pp+0xa7e0] "次为0"
    //     0x6c992c: ldr             x16, [x16, #0x7e0]
    // 0x6c9930: ArrayStore: r0[0] = r16  ; List_4
    //     0x6c9930: stur            w16, [x0, #0x17]
    // 0x6c9934: str             x0, [SP]
    // 0x6c9938: r0 = _interpolate()
    //     0x6c9938: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c993c: ldur            x1, [fp, #-0x18]
    // 0x6c9940: mov             x2, x0
    // 0x6c9944: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c9944: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c9948: r0 = w()
    //     0x6c9948: bl              #0x61c68c  ; [package:logger/src/logger.dart] Logger::w
    // 0x6c994c: r0 = false
    //     0x6c994c: add             x0, NULL, #0x30  ; false
    // 0x6c9950: LeaveFrame
    //     0x6c9950: mov             SP, fp
    //     0x6c9954: ldp             fp, lr, [SP], #0x10
    // 0x6c9958: ret
    //     0x6c9958: ret             
    // 0x6c995c: ldur            x3, [fp, #-0x10]
    // 0x6c9960: LoadField: r0 = r3->field_27
    //     0x6c9960: ldur            x0, [x3, #0x27]
    // 0x6c9964: cmp             x0, #2
    // 0x6c9968: b.lt            #0x6c9a88
    // 0x6c996c: ldur            x1, [fp, #-8]
    // 0x6c9970: LoadField: r0 = r1->field_b
    //     0x6c9970: ldur            w0, [x1, #0xb]
    // 0x6c9974: r2 = LoadInt32Instr(r0)
    //     0x6c9974: sbfx            x2, x0, #1, #0x1f
    // 0x6c9978: cmp             x2, #3
    // 0x6c997c: b.lt            #0x6c9a88
    // 0x6c9980: r2 = 3
    //     0x6c9980: movz            x2, #0x3
    // 0x6c9984: r0 = take()
    //     0x6c9984: bl              #0x9e3eb8  ; [dart:collection] ListBase::take
    // 0x6c9988: r1 = Function '<anonymous closure>':.
    //     0x6c9988: add             x1, PP, #0xa, lsl #12  ; [pp+0xa7e8] AnonymousClosure: (0x6c981c), in [package:keepdance/pose/utils/pose_plugin_manager.dart] PosePluginManager::_shouldAllowDowngrade (0x6c9770)
    //     0x6c998c: ldr             x1, [x1, #0x7e8]
    // 0x6c9990: r2 = Null
    //     0x6c9990: mov             x2, NULL
    // 0x6c9994: stur            x0, [fp, #-8]
    // 0x6c9998: r0 = AllocateClosure()
    //     0x6c9998: bl              #0xf81a3c  ; AllocateClosureStub
    // 0x6c999c: ldur            x1, [fp, #-8]
    // 0x6c99a0: mov             x2, x0
    // 0x6c99a4: r0 = reduce()
    //     0x6c99a4: bl              #0x82fc3c  ; [dart:_internal] ListIterable::reduce
    // 0x6c99a8: r1 = LoadInt32Instr(r0)
    //     0x6c99a8: sbfx            x1, x0, #1, #0x1f
    //     0x6c99ac: tbz             w0, #0, #0x6c99b4
    //     0x6c99b0: ldur            x1, [x0, #7]
    // 0x6c99b4: scvtf           d0, x1
    // 0x6c99b8: d1 = 3.000000
    //     0x6c99b8: fmov            d1, #3.00000000
    // 0x6c99bc: fdiv            d2, d0, d1
    // 0x6c99c0: stur            d2, [fp, #-0x28]
    // 0x6c99c4: d0 = 1.200000
    //     0x6c99c4: add             x17, PP, #0xa, lsl #12  ; [pp+0xa7f0] IMM: double(1.2) from 0x3ff3333333333333
    //     0x6c99c8: ldr             d0, [x17, #0x7f0]
    // 0x6c99cc: fcmp            d0, d2
    // 0x6c99d0: b.le            #0x6c9a88
    // 0x6c99d4: ldur            x0, [fp, #-0x10]
    // 0x6c99d8: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x6c99d8: ldur            w3, [x0, #0x17]
    // 0x6c99dc: DecompressPointer r3
    //     0x6c99dc: add             x3, x3, HEAP, lsl #32
    // 0x6c99e0: stur            x3, [fp, #-8]
    // 0x6c99e4: r1 = Null
    //     0x6c99e4: mov             x1, NULL
    // 0x6c99e8: r2 = 4
    //     0x6c99e8: movz            x2, #0x4
    // 0x6c99ec: r0 = AllocateArray()
    //     0x6c99ec: bl              #0xf82714  ; AllocateArrayStub
    // 0x6c99f0: stur            x0, [fp, #-0x10]
    // 0x6c99f4: r16 = "PosePluginManager: 双人检测质量不佳，平均检测人数: "
    //     0x6c99f4: add             x16, PP, #0xa, lsl #12  ; [pp+0xa7f8] "PosePluginManager: 双人检测质量不佳，平均检测人数: "
    //     0x6c99f8: ldr             x16, [x16, #0x7f8]
    // 0x6c99fc: StoreField: r0->field_f = r16
    //     0x6c99fc: stur            w16, [x0, #0xf]
    // 0x6c9a00: ldur            d0, [fp, #-0x28]
    // 0x6c9a04: r1 = inline_Allocate_Double()
    //     0x6c9a04: ldp             x1, x2, [THR, #0x50]  ; THR::top
    //     0x6c9a08: add             x1, x1, #0x10
    //     0x6c9a0c: cmp             x2, x1
    //     0x6c9a10: b.ls            #0x6c9aa0
    //     0x6c9a14: str             x1, [THR, #0x50]  ; THR::top
    //     0x6c9a18: sub             x1, x1, #0xf
    //     0x6c9a1c: movz            x2, #0xd15c
    //     0x6c9a20: movk            x2, #0x3, lsl #16
    //     0x6c9a24: stur            x2, [x1, #-1]
    // 0x6c9a28: StoreField: r1->field_7 = d0
    //     0x6c9a28: stur            d0, [x1, #7]
    // 0x6c9a2c: r2 = 1
    //     0x6c9a2c: movz            x2, #0x1
    // 0x6c9a30: r0 = toStringAsFixed()
    //     0x6c9a30: bl              #0x6c9abc  ; [dart:core] _Double::toStringAsFixed
    // 0x6c9a34: ldur            x1, [fp, #-0x10]
    // 0x6c9a38: ArrayStore: r1[1] = r0  ; List_4
    //     0x6c9a38: add             x25, x1, #0x13
    //     0x6c9a3c: str             w0, [x25]
    //     0x6c9a40: tbz             w0, #0, #0x6c9a5c
    //     0x6c9a44: ldurb           w16, [x1, #-1]
    //     0x6c9a48: ldurb           w17, [x0, #-1]
    //     0x6c9a4c: and             x16, x17, x16, lsr #2
    //     0x6c9a50: tst             x16, HEAP, lsr #32
    //     0x6c9a54: b.eq            #0x6c9a5c
    //     0x6c9a58: bl              #0xf808e8  ; ArrayWriteBarrierStub
    // 0x6c9a5c: ldur            x16, [fp, #-0x10]
    // 0x6c9a60: str             x16, [SP]
    // 0x6c9a64: r0 = _interpolate()
    //     0x6c9a64: bl              #0x5f8b18  ; [dart:core] _StringBase::_interpolate
    // 0x6c9a68: ldur            x1, [fp, #-8]
    // 0x6c9a6c: mov             x2, x0
    // 0x6c9a70: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x6c9a70: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x6c9a74: r0 = w()
    //     0x6c9a74: bl              #0x61c68c  ; [package:logger/src/logger.dart] Logger::w
    // 0x6c9a78: r0 = false
    //     0x6c9a78: add             x0, NULL, #0x30  ; false
    // 0x6c9a7c: LeaveFrame
    //     0x6c9a7c: mov             SP, fp
    //     0x6c9a80: ldp             fp, lr, [SP], #0x10
    // 0x6c9a84: ret
    //     0x6c9a84: ret             
    // 0x6c9a88: r0 = true
    //     0x6c9a88: add             x0, NULL, #0x20  ; true
    // 0x6c9a8c: LeaveFrame
    //     0x6c9a8c: mov             SP, fp
    //     0x6c9a90: ldp             fp, lr, [SP], #0x10
    // 0x6c9a94: ret
    //     0x6c9a94: ret             
    // 0x6c9a98: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c9a98: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c9a9c: b               #0x6c9888
    // 0x6c9aa0: SaveReg d0
    //     0x6c9aa0: str             q0, [SP, #-0x10]!
    // 0x6c9aa4: SaveReg r0
    //     0x6c9aa4: str             x0, [SP, #-8]!
    // 0x6c9aa8: r0 = AllocateDouble()
    //     0x6c9aa8: bl              #0xf8266c  ; AllocateDoubleStub
    // 0x6c9aac: mov             x1, x0
    // 0x6c9ab0: RestoreReg r0
    //     0x6c9ab0: ldr             x0, [SP], #8
    // 0x6c9ab4: RestoreReg d0
    //     0x6c9ab4: ldr             q0, [SP], #0x10
    // 0x6c9ab8: b               #0x6c9a28
  }
  [closure] bool <anonymous closure>(dynamic, int) {
    // ** addr: 0x6c9c24, size: 0x18
    // 0x6c9c24: ldr             x1, [SP]
    // 0x6c9c28: cbz             w1, #0x6c9c34
    // 0x6c9c2c: r0 = false
    //     0x6c9c2c: add             x0, NULL, #0x30  ; false
    // 0x6c9c30: b               #0x6c9c38
    // 0x6c9c34: r0 = true
    //     0x6c9c34: add             x0, NULL, #0x20  ; true
    // 0x6c9c38: ret
    //     0x6c9c38: ret             
  }
  _ PosePluginManager(/* No info */) {
    // ** addr: 0x6c9c3c, size: 0x140
    // 0x6c9c3c: EnterFrame
    //     0x6c9c3c: stp             fp, lr, [SP, #-0x10]!
    //     0x6c9c40: mov             fp, SP
    // 0x6c9c44: AllocStack(0x20)
    //     0x6c9c44: sub             SP, SP, #0x20
    // 0x6c9c48: r2 = false
    //     0x6c9c48: add             x2, NULL, #0x30  ; false
    // 0x6c9c4c: r0 = 1
    //     0x6c9c4c: movz            x0, #0x1
    // 0x6c9c50: stur            x1, [fp, #-8]
    // 0x6c9c54: CheckStackOverflow
    //     0x6c9c54: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x6c9c58: cmp             SP, x16
    //     0x6c9c5c: b.ls            #0x6c9d74
    // 0x6c9c60: StoreField: r1->field_23 = r2
    //     0x6c9c60: stur            w2, [x1, #0x23]
    // 0x6c9c64: StoreField: r1->field_27 = r0
    //     0x6c9c64: stur            x0, [x1, #0x27]
    // 0x6c9c68: r0 = PrettyPrinter()
    //     0x6c9c68: bl              #0x66a180  ; AllocatePrettyPrinterStub -> PrettyPrinter (size=0x58)
    // 0x6c9c6c: stur            x0, [fp, #-0x10]
    // 0x6c9c70: r16 = 2
    //     0x6c9c70: movz            x16, #0x2
    // 0x6c9c74: str             x16, [SP]
    // 0x6c9c78: mov             x1, x0
    // 0x6c9c7c: r4 = const [0, 0x2, 0x1, 0x1, methodCount, 0x1, null]
    //     0x6c9c7c: add             x4, PP, #0xa, lsl #12  ; [pp+0xa890] List(7) [0, 0x2, 0x1, 0x1, "methodCount", 0x1, Null]
    //     0x6c9c80: ldr             x4, [x4, #0x890]
    // 0x6c9c84: r0 = PrettyPrinter()
    //     0x6c9c84: bl              #0x668c50  ; [package:logger/src/printers/pretty_printer.dart] PrettyPrinter::PrettyPrinter
    // 0x6c9c88: r0 = Logger()
    //     0x6c9c88: bl              #0x66a1d0  ; AllocateLoggerStub -> Logger (size=0x1c)
    // 0x6c9c8c: stur            x0, [fp, #-0x18]
    // 0x6c9c90: ldur            x16, [fp, #-0x10]
    // 0x6c9c94: str             x16, [SP]
    // 0x6c9c98: mov             x1, x0
    // 0x6c9c9c: r4 = const [0, 0x2, 0x1, 0x1, printer, 0x1, null]
    //     0x6c9c9c: ldr             x4, [PP, #0x3a90]  ; [pp+0x3a90] List(7) [0, 0x2, 0x1, 0x1, "printer", 0x1, Null]
    // 0x6c9ca0: r0 = Logger()
    //     0x6c9ca0: bl              #0x667d30  ; [package:logger/src/logger.dart] Logger::Logger
    // 0x6c9ca4: ldur            x0, [fp, #-0x18]
    // 0x6c9ca8: ldur            x1, [fp, #-8]
    // 0x6c9cac: ArrayStore: r1[0] = r0  ; List_4
    //     0x6c9cac: stur            w0, [x1, #0x17]
    //     0x6c9cb0: ldurb           w16, [x1, #-1]
    //     0x6c9cb4: ldurb           w17, [x0, #-1]
    //     0x6c9cb8: and             x16, x17, x16, lsr #2
    //     0x6c9cbc: tst             x16, HEAP, lsr #32
    //     0x6c9cc0: b.eq            #0x6c9cc8
    //     0x6c9cc4: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6c9cc8: r0 = PosePlugin()
    //     0x6c9cc8: bl              #0x6c9d7c  ; AllocatePosePluginStub -> PosePlugin (size=0x18)
    // 0x6c9ccc: mov             x1, x0
    // 0x6c9cd0: r0 = false
    //     0x6c9cd0: add             x0, NULL, #0x30  ; false
    // 0x6c9cd4: StoreField: r1->field_b = r0
    //     0x6c9cd4: stur            w0, [x1, #0xb]
    // 0x6c9cd8: r0 = 1
    //     0x6c9cd8: movz            x0, #0x1
    // 0x6c9cdc: StoreField: r1->field_f = r0
    //     0x6c9cdc: stur            x0, [x1, #0xf]
    // 0x6c9ce0: mov             x0, x1
    // 0x6c9ce4: ldur            x2, [fp, #-8]
    // 0x6c9ce8: StoreField: r2->field_1b = r0
    //     0x6c9ce8: stur            w0, [x2, #0x1b]
    //     0x6c9cec: ldurb           w16, [x2, #-1]
    //     0x6c9cf0: ldurb           w17, [x0, #-1]
    //     0x6c9cf4: and             x16, x17, x16, lsr #2
    //     0x6c9cf8: tst             x16, HEAP, lsr #32
    //     0x6c9cfc: b.eq            #0x6c9d04
    //     0x6c9d00: bl              #0xf80e54  ; WriteBarrierWrappersStub
    // 0x6c9d04: r1 = <List<List<PoseJoint>>>
    //     0x6c9d04: add             x1, PP, #0xa, lsl #12  ; [pp+0xa898] TypeArguments: <List<List<PoseJoint>>>
    //     0x6c9d08: ldr             x1, [x1, #0x898]
    // 0x6c9d0c: r4 = const [0, 0x1, 0, 0x1, null]
    //     0x6c9d0c: ldr             x4, [PP, #0x1f0]  ; [pp+0x1f0] List(5) [0, 0x1, 0, 0x1, Null]
    // 0x6c9d10: r0 = StreamController.broadcast()
    //     0x6c9d10: bl              #0x6b42a8  ; [dart:async] StreamController::StreamController.broadcast
    // 0x6c9d14: ldur            x3, [fp, #-8]
    // 0x6c9d18: StoreField: r3->field_1f = r0
    //     0x6c9d18: stur            w0, [x3, #0x1f]
    //     0x6c9d1c: ldurb           w16, [x3, #-1]
    //     0x6c9d20: ldurb           w17, [x0, #-1]
    //     0x6c9d24: and             x16, x17, x16, lsr #2
    //     0x6c9d28: tst             x16, HEAP, lsr #32
    //     0x6c9d2c: b.eq            #0x6c9d34
    //     0x6c9d30: bl              #0xf80e74  ; WriteBarrierWrappersStub
    // 0x6c9d34: r1 = <int>
    //     0x6c9d34: ldr             x1, [PP, #0xff8]  ; [pp+0xff8] TypeArguments: <int>
    // 0x6c9d38: r2 = 0
    //     0x6c9d38: movz            x2, #0
    // 0x6c9d3c: r0 = _GrowableList()
    //     0x6c9d3c: bl              #0x5fae50  ; [dart:core] _GrowableList::_GrowableList
    // 0x6c9d40: ldur            x1, [fp, #-8]
    // 0x6c9d44: StoreField: r1->field_33 = r0
    //     0x6c9d44: stur            w0, [x1, #0x33]
    //     0x6c9d48: ldurb           w16, [x1, #-1]
    //     0x6c9d4c: ldurb           w17, [x0, #-1]
    //     0x6c9d50: and             x16, x17, x16, lsr #2
    //     0x6c9d54: tst             x16, HEAP, lsr #32
    //     0x6c9d58: b.eq            #0x6c9d60
    //     0x6c9d5c: bl              #0xf80e34  ; WriteBarrierWrappersStub
    // 0x6c9d60: r0 = GetxService()
    //     0x6c9d60: bl              #0x6c9fac  ; [package:get/get_state_manager/src/rx_flutter/rx_disposable.dart] GetxService::GetxService
    // 0x6c9d64: r0 = Null
    //     0x6c9d64: mov             x0, NULL
    // 0x6c9d68: LeaveFrame
    //     0x6c9d68: mov             SP, fp
    //     0x6c9d6c: ldp             fp, lr, [SP], #0x10
    // 0x6c9d70: ret
    //     0x6c9d70: ret             
    // 0x6c9d74: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x6c9d74: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x6c9d78: b               #0x6c9c60
  }
  get _ poseStream(/* No info */) {
    // ** addr: 0x73a738, size: 0x38
    // 0x73a738: EnterFrame
    //     0x73a738: stp             fp, lr, [SP, #-0x10]!
    //     0x73a73c: mov             fp, SP
    // 0x73a740: AllocStack(0x8)
    //     0x73a740: sub             SP, SP, #8
    // 0x73a744: LoadField: r0 = r1->field_1f
    //     0x73a744: ldur            w0, [x1, #0x1f]
    // 0x73a748: DecompressPointer r0
    //     0x73a748: add             x0, x0, HEAP, lsl #32
    // 0x73a74c: stur            x0, [fp, #-8]
    // 0x73a750: LoadField: r1 = r0->field_7
    //     0x73a750: ldur            w1, [x0, #7]
    // 0x73a754: DecompressPointer r1
    //     0x73a754: add             x1, x1, HEAP, lsl #32
    // 0x73a758: r0 = _BroadcastStream()
    //     0x73a758: bl              #0x68cc84  ; Allocate_BroadcastStreamStub -> _BroadcastStream<X0> (size=0x10)
    // 0x73a75c: ldur            x1, [fp, #-8]
    // 0x73a760: StoreField: r0->field_b = r1
    //     0x73a760: stur            w1, [x0, #0xb]
    // 0x73a764: LeaveFrame
    //     0x73a764: mov             SP, fp
    //     0x73a768: ldp             fp, lr, [SP], #0x10
    // 0x73a76c: ret
    //     0x73a76c: ret             
  }
  _ resetForArchitectureSwitch(/* No info */) {
    // ** addr: 0x969c00, size: 0xa8
    // 0x969c00: EnterFrame
    //     0x969c00: stp             fp, lr, [SP, #-0x10]!
    //     0x969c04: mov             fp, SP
    // 0x969c08: AllocStack(0x10)
    //     0x969c08: sub             SP, SP, #0x10
    // 0x969c0c: SetupParameters(PosePluginManager this /* r1 => r0, fp-0x10 */)
    //     0x969c0c: mov             x0, x1
    //     0x969c10: stur            x1, [fp, #-0x10]
    // 0x969c14: CheckStackOverflow
    //     0x969c14: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x969c18: cmp             SP, x16
    //     0x969c1c: b.ls            #0x969ca0
    // 0x969c20: ArrayLoad: r3 = r0[0]  ; List_4
    //     0x969c20: ldur            w3, [x0, #0x17]
    // 0x969c24: DecompressPointer r3
    //     0x969c24: add             x3, x3, HEAP, lsl #32
    // 0x969c28: mov             x1, x3
    // 0x969c2c: stur            x3, [fp, #-8]
    // 0x969c30: r2 = "PosePluginManager: 🔄 架构切换状态重置"
    //     0x969c30: add             x2, PP, #0xe, lsl #12  ; [pp+0xecf8] "PosePluginManager: 🔄 架构切换状态重置"
    //     0x969c34: ldr             x2, [x2, #0xcf8]
    // 0x969c38: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x969c38: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x969c3c: r0 = i()
    //     0x969c3c: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x969c40: ldur            x2, [fp, #-0x10]
    // 0x969c44: r0 = false
    //     0x969c44: add             x0, NULL, #0x30  ; false
    // 0x969c48: StoreField: r2->field_23 = r0
    //     0x969c48: stur            w0, [x2, #0x23]
    // 0x969c4c: r1 = 1
    //     0x969c4c: movz            x1, #0x1
    // 0x969c50: StoreField: r2->field_27 = r1
    //     0x969c50: stur            x1, [x2, #0x27]
    // 0x969c54: StoreStaticField(0x1408, r0)
    //     0x969c54: ldr             x1, [THR, #0x68]  ; THR::field_table_values
    //     0x969c58: str             x0, [x1, #0x2810]
    // 0x969c5c: LoadField: r1 = r2->field_33
    //     0x969c5c: ldur            w1, [x2, #0x33]
    // 0x969c60: DecompressPointer r1
    //     0x969c60: add             x1, x1, HEAP, lsl #32
    // 0x969c64: r0 = clear()
    //     0x969c64: bl              #0x785e80  ; [dart:core] _GrowableList::clear
    // 0x969c68: ldur            x0, [fp, #-0x10]
    // 0x969c6c: StoreField: r0->field_2f = rNULL
    //     0x969c6c: stur            NULL, [x0, #0x2f]
    // 0x969c70: LoadField: r1 = r0->field_1b
    //     0x969c70: ldur            w1, [x0, #0x1b]
    // 0x969c74: DecompressPointer r1
    //     0x969c74: add             x1, x1, HEAP, lsl #32
    // 0x969c78: StoreField: r1->field_7 = rNULL
    //     0x969c78: stur            NULL, [x1, #7]
    // 0x969c7c: ldur            x1, [fp, #-8]
    // 0x969c80: r2 = "PosePluginManager: ✅ 架构切换状态重置完成"
    //     0x969c80: add             x2, PP, #0xe, lsl #12  ; [pp+0xed00] "PosePluginManager: ✅ 架构切换状态重置完成"
    //     0x969c84: ldr             x2, [x2, #0xd00]
    // 0x969c88: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x969c88: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x969c8c: r0 = i()
    //     0x969c8c: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x969c90: r0 = Null
    //     0x969c90: mov             x0, NULL
    // 0x969c94: LeaveFrame
    //     0x969c94: mov             SP, fp
    //     0x969c98: ldp             fp, lr, [SP], #0x10
    // 0x969c9c: ret
    //     0x969c9c: ret             
    // 0x969ca0: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x969ca0: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x969ca4: b               #0x969c20
  }
  _ onClose(/* No info */) {
    // ** addr: 0x96d880, size: 0x60
    // 0x96d880: EnterFrame
    //     0x96d880: stp             fp, lr, [SP, #-0x10]!
    //     0x96d884: mov             fp, SP
    // 0x96d888: AllocStack(0x8)
    //     0x96d888: sub             SP, SP, #8
    // 0x96d88c: SetupParameters(PosePluginManager this /* r1 => r0, fp-0x8 */)
    //     0x96d88c: mov             x0, x1
    //     0x96d890: stur            x1, [fp, #-8]
    // 0x96d894: CheckStackOverflow
    //     0x96d894: ldr             x16, [THR, #0x38]  ; THR::stack_limit
    //     0x96d898: cmp             SP, x16
    //     0x96d89c: b.ls            #0x96d8d8
    // 0x96d8a0: ArrayLoad: r1 = r0[0]  ; List_4
    //     0x96d8a0: ldur            w1, [x0, #0x17]
    // 0x96d8a4: DecompressPointer r1
    //     0x96d8a4: add             x1, x1, HEAP, lsl #32
    // 0x96d8a8: r2 = "PosePluginManager: 正在关闭..."
    //     0x96d8a8: add             x2, PP, #0x11, lsl #12  ; [pp+0x11e98] "PosePluginManager: 正在关闭..."
    //     0x96d8ac: ldr             x2, [x2, #0xe98]
    // 0x96d8b0: r4 = const [0, 0x2, 0, 0x2, null]
    //     0x96d8b0: ldr             x4, [PP, #0x408]  ; [pp+0x408] List(5) [0, 0x2, 0, 0x2, Null]
    // 0x96d8b4: r0 = i()
    //     0x96d8b4: bl              #0x6bd4b0  ; [package:logger/src/logger.dart] Logger::i
    // 0x96d8b8: ldur            x0, [fp, #-8]
    // 0x96d8bc: LoadField: r1 = r0->field_1f
    //     0x96d8bc: ldur            w1, [x0, #0x1f]
    // 0x96d8c0: DecompressPointer r1
    //     0x96d8c0: add             x1, x1, HEAP, lsl #32
    // 0x96d8c4: r0 = close()
    //     0x96d8c4: bl              #0x71e364  ; [dart:async] _BroadcastStreamController::close
    // 0x96d8c8: r0 = Null
    //     0x96d8c8: mov             x0, NULL
    // 0x96d8cc: LeaveFrame
    //     0x96d8cc: mov             SP, fp
    //     0x96d8d0: ldp             fp, lr, [SP], #0x10
    // 0x96d8d4: ret
    //     0x96d8d4: ret             
    // 0x96d8d8: r0 = StackOverflowSharedWithoutFPURegs()
    //     0x96d8d8: bl              #0xf8281c  ; StackOverflowSharedWithoutFPURegsStub
    // 0x96d8dc: b               #0x96d8a0
  }
}
